// ProcessingHelper.ts
import fs from "node:fs"
import path from "node:path"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import * as axios from "axios"
import { app, BrowserWindow, dialog } from "electron"
import { OpenAI } from "openai"
import { configHelper } from "./ConfigHelper"
import Anthropic from '@anthropic-ai/sdk';

// Interface for Gemini API requests
interface GeminiMessage {
  role: string;
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    }
  }>;
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
}
interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: Array<{
    type: 'text' | 'image';
    text?: string;
    source?: {
      type: 'base64';
      media_type: string;
      data: string;
    };
  }>;
}
export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper
  private openaiClient: OpenAI | null = null
  private geminiApiKey: string | null = null
  private anthropicClient: Anthropic | null = null

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()
    
    // Initialize AI client based on config
    this.initializeAIClient();
    
    // Listen for config changes to re-initialize the AI client
    configHelper.on('config-updated', () => {
      this.initializeAIClient();
    });
  }
  
  /**
   * Initialize or reinitialize the AI client with current config
   */
  private initializeAIClient(): void {
    try {
      const config = configHelper.loadConfig();
      
      if (config.apiProvider === "openai") {
        if (config.apiKey) {
          this.openaiClient = new OpenAI({ 
            apiKey: config.apiKey,
            timeout: 60000, // 60 second timeout
            maxRetries: 2   // Retry up to 2 times
          });
          this.geminiApiKey = null;
          this.anthropicClient = null;
          console.log("OpenAI client initialized successfully");
        } else {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          console.warn("No API key available, OpenAI client not initialized");
        }
      } else if (config.apiProvider === "gemini"){
        // Gemini client initialization
        this.openaiClient = null;
        this.anthropicClient = null;
        if (config.apiKey) {
          this.geminiApiKey = config.apiKey;
          console.log("Gemini API key set successfully");
        } else {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          console.warn("No API key available, Gemini client not initialized");
        }
      } else if (config.apiProvider === "anthropic") {
        // Reset other clients
        this.openaiClient = null;
        this.geminiApiKey = null;
        if (config.apiKey) {
          this.anthropicClient = new Anthropic({
            apiKey: config.apiKey,
            timeout: 60000,
            maxRetries: 2
          });
          console.log("Anthropic client initialized successfully");
        } else {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          console.warn("No API key available, Anthropic client not initialized");
        }
      }
    } catch (error) {
      console.error("Failed to initialize AI client:", error);
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
    }
  }

  private async waitForInitialization(
    mainWindow: BrowserWindow
  ): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  private async getCredits(): Promise<number> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return 999 // Unlimited credits in this version

    try {
      await this.waitForInitialization(mainWindow)
      return 999 // Always return sufficient credits to work
    } catch (error) {
      console.error("Error getting credits:", error)
      return 999 // Unlimited credits as fallback
    }
  }

  private async getLanguage(): Promise<string> {
    try {
      // Get language from config
      const config = configHelper.loadConfig();
      if (config.language) {
        return config.language;
      }
      
      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        try {
          await this.waitForInitialization(mainWindow)
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          ) {
            return language;
          }
        } catch (err) {
          console.warn("Could not get language from window", err);
        }
      }
      
      // Default fallback
      return "python";
    } catch (error) {
      console.error("Error getting language:", error)
      return "python"
    }
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();
    
    // First verify we have a valid AI client
    if (config.apiProvider === "openai" && !this.openaiClient) {
      this.initializeAIClient();
      
      if (!this.openaiClient) {
        console.error("OpenAI client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "gemini" && !this.geminiApiKey) {
      this.initializeAIClient();
      
      if (!this.geminiApiKey) {
        console.error("Gemini API key not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "anthropic" && !this.anthropicClient) {
      // Add check for Anthropic client
      this.initializeAIClient();
      
      if (!this.anthropicClient) {
        console.error("Anthropic client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    }

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)
      
      // Check if the queue is empty
      if (!screenshotQueue || screenshotQueue.length === 0) {
        console.log("No screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Check that files actually exist
      const existingScreenshots = screenshotQueue.filter(path => fs.existsSync(path));
      if (existingScreenshots.length === 0) {
        console.log("Screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          existingScreenshots.map(async (path) => {
            try {
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);
        
        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data");
        }

        const result = await this.processScreenshotsHelper(validScreenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("API Key") || result.error?.includes("OpenAI") || result.error?.includes("Gemini")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // Reset view back to queue on error
          console.log("Resetting view to queue due to error")
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView("solutions")
      } catch (error: any) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error("Processing error:", error)
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message || "Server error. Please try again."
          )
        }
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)
      
      // Check if the extra queue is empty
      if (!extraScreenshotQueue || extraScreenshotQueue.length === 0) {
        console.log("No extra screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        
        return;
      }

      // Check that files actually exist
      const existingExtraScreenshots = extraScreenshotQueue.filter(path => fs.existsSync(path));
      if (existingExtraScreenshots.length === 0) {
        console.log("Extra screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }
      
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        // Get all screenshots (both main and extra) for processing
        const allPaths = [
          ...this.screenshotHelper.getScreenshotQueue(),
          ...existingExtraScreenshots
        ];
        
        const screenshots = await Promise.all(
          allPaths.map(async (path) => {
            try {
              if (!fs.existsSync(path)) {
                console.warn(`Screenshot file does not exist: ${path}`);
                return null;
              }
              
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )
        
        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);
        
        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data for debugging");
        }
        
        console.log(
          "Combined screenshots for processing:",
          validScreenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          validScreenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch (error: any) {
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            error.message
          )
        }
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();
      
      // Step 1: Extract problem info using AI Vision API (OpenAI or Gemini)
      const imageDataList = screenshots.map(screenshot => screenshot.data);
      
      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Analyzing problem from screenshots...",
          progress: 20
        });
      }

      let problemInfo;
      
      if (config.apiProvider === "openai") {
        // Verify OpenAI client
        if (!this.openaiClient) {
          this.initializeAIClient(); // Try to reinitialize
          
          if (!this.openaiClient) {
            return {
              success: false,
              error: "OpenAI API key not configured or invalid. Please check your settings."
            };
          }
        }

        // Use OpenAI for processing
        const messages = [
          {
            role: "system" as const,
            content: `You are an intelligent problem interpreter that can analyze different types of programming and computer science questions. Analyze the screenshots and determine the problem type, then extract all relevant information.

PROBLEM TYPES TO DETECT:
1. "coding" - Traditional coding problems requiring implementation (e.g., "Write a function that...", "Implement an algorithm...")
2. "multiple_choice" - Questions with selectable options, including:
   - Questions with checkboxes or radio buttons
   - Questions asking "What are the correct statements..." with multiple options
   - Questions with "(Select all acceptable answers.)" or similar instructions
   - Any question format where users select from predefined options
3. "conceptual" - Theoretical questions about algorithms, data structures, or CS concepts
4. "algorithm_analysis" - Questions about time/space complexity, algorithm comparison, etc.

DETECTION CLUES FOR MULTIPLE CHOICE:
- Look for checkboxes (☐) or radio buttons (○)
- Text like "Select all acceptable answers", "Choose the correct option", "What are the correct statements"
- Multiple numbered or bulleted options/statements
- Questions asking about properties or characteristics of given code

REQUIRED JSON STRUCTURE:
{
  "problem_statement": "The main question or problem description",
  "problem_type": "coding|multiple_choice|conceptual|algorithm_analysis",
  "constraints": "Any constraints or limitations (if applicable)",
  "example_input": "Example input (for coding problems)",
  "example_output": "Example output (for coding problems)",
  "multiple_choice_options": ["Option 1 text", "Option 2 text", "Option 3 text", "Option 4 text"] (for multiple choice - extract the full text of each option),
  "correct_answer": "The correct answer(s) if visible in screenshots",
  "explanation": "Any explanation provided (if visible)",
  "key_concepts": ["concept1", "concept2"] (for conceptual questions),
  "difficulty": "easy|medium|hard (if mentioned)"
}

IMPORTANT: If you see checkboxes, selectable options, or text like "Select all acceptable answers", this is DEFINITELY a multiple_choice question type.

Return ONLY the JSON without any other text.`
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `Analyze these screenshots and extract the problem information. Determine if this is a coding problem, multiple choice question, conceptual question, or algorithm analysis question. Extract all relevant details based on the problem type. Preferred coding language: ${language}.`
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        // Send to OpenAI Vision API
        const extractionResponse = await this.openaiClient.chat.completions.create({
          model: config.extractionModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        });

        // Parse the response
        try {
          const responseText = extractionResponse.choices[0].message.content;
          // Handle when OpenAI might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error parsing OpenAI response:", error);
          return {
            success: false,
            error: "Failed to parse problem information. Please try again or use clearer screenshots."
          };
        }
      } else if (config.apiProvider === "gemini")  {
        // Use Gemini API
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          // Create Gemini message structure
          const geminiMessages: GeminiMessage[] = [
            {
              role: "user",
              parts: [
                {
                  text: `You are an intelligent problem interpreter that can analyze different types of programming and computer science questions. Analyze the screenshots and determine the problem type, then extract all relevant information.

PROBLEM TYPES TO DETECT:
1. "coding" - Traditional coding problems requiring implementation (e.g., "Write a function that...", "Implement an algorithm...")
2. "multiple_choice" - Questions with selectable options, including:
   - Questions with checkboxes or radio buttons
   - Questions asking "What are the correct statements..." with multiple options
   - Questions with "(Select all acceptable answers.)" or similar instructions
   - Any question format where users select from predefined options
3. "conceptual" - Theoretical questions about algorithms, data structures, or CS concepts
4. "algorithm_analysis" - Questions about time/space complexity, algorithm comparison, etc.

DETECTION CLUES FOR MULTIPLE CHOICE:
- Look for checkboxes (☐) or radio buttons (○)
- Text like "Select all acceptable answers", "Choose the correct option", "What are the correct statements"
- Multiple numbered or bulleted options/statements
- Questions asking about properties or characteristics of given code

REQUIRED JSON STRUCTURE:
{
  "problem_statement": "The main question or problem description",
  "problem_type": "coding|multiple_choice|conceptual|algorithm_analysis",
  "constraints": "Any constraints or limitations (if applicable)",
  "example_input": "Example input (for coding problems)",
  "example_output": "Example output (for coding problems)",
  "multiple_choice_options": ["Option 1 text", "Option 2 text", "Option 3 text", "Option 4 text"] (for multiple choice - extract the full text of each option),
  "correct_answer": "The correct answer(s) if visible in screenshots",
  "explanation": "Any explanation provided (if visible)",
  "key_concepts": ["concept1", "concept2"] (for conceptual questions),
  "difficulty": "easy|medium|hard (if mentioned)"
}

IMPORTANT: If you see checkboxes, selectable options, or text like "Select all acceptable answers", this is DEFINITELY a multiple_choice question type.

Analyze these screenshots and extract the problem information. Determine the problem type and extract relevant details. Preferred coding language: ${language}. Return ONLY the JSON without any other text.`
                },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.extractionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;
          
          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }
          
          const responseText = responseData.candidates[0].content.parts[0].text;
          
          // Handle when Gemini might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error using Gemini API:", error);
          return {
            success: false,
            error: "Failed to process with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `You are an intelligent problem interpreter that can analyze different types of programming and computer science questions. Analyze the screenshots and determine the problem type, then extract all relevant information.

PROBLEM TYPES TO DETECT:
1. "coding" - Traditional coding problems requiring implementation (e.g., "Write a function that...", "Implement an algorithm...")
2. "multiple_choice" - Questions with selectable options, including:
   - Questions with checkboxes or radio buttons
   - Questions asking "What are the correct statements..." with multiple options
   - Questions with "(Select all acceptable answers.)" or similar instructions
   - Any question format where users select from predefined options
3. "conceptual" - Theoretical questions about algorithms, data structures, or CS concepts
4. "algorithm_analysis" - Questions about time/space complexity, algorithm comparison, etc.

DETECTION CLUES FOR MULTIPLE CHOICE:
- Look for checkboxes (☐) or radio buttons (○)
- Text like "Select all acceptable answers", "Choose the correct option", "What are the correct statements"
- Multiple numbered or bulleted options/statements
- Questions asking about properties or characteristics of given code

REQUIRED JSON STRUCTURE:
{
  "problem_statement": "The main question or problem description",
  "problem_type": "coding|multiple_choice|conceptual|algorithm_analysis",
  "constraints": "Any constraints or limitations (if applicable)",
  "example_input": "Example input (for coding problems)",
  "example_output": "Example output (for coding problems)",
  "multiple_choice_options": ["Option 1 text", "Option 2 text", "Option 3 text", "Option 4 text"] (for multiple choice - extract the full text of each option),
  "correct_answer": "The correct answer(s) if visible in screenshots",
  "explanation": "Any explanation provided (if visible)",
  "key_concepts": ["concept1", "concept2"] (for conceptual questions),
  "difficulty": "easy|medium|hard (if mentioned)"
}

IMPORTANT: If you see checkboxes, selectable options, or text like "Select all acceptable answers", this is DEFINITELY a multiple_choice question type.

Analyze these screenshots and extract the problem information. Determine the problem type and extract relevant details. Preferred coding language: ${language}. Return ONLY the JSON without any other text.`
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                }))
              ]
            }
          ];

          const response = await this.anthropicClient.messages.create({
            model: config.extractionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          const responseText = (response.content[0] as { type: 'text', text: string }).text;
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error: any) {
          console.error("Error using Anthropic API:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process with Anthropic API. Please check your API key or try again later."
          };
        }
      }
      
      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Problem analyzed successfully. Preparing to generate solution...",
          progress: 40
        });
      }

      // Store problem info in AppState
      this.deps.setProblemInfo(problemInfo);

      // Send first success event
      if (mainWindow) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
          problemInfo
        );

        // Generate solutions after successful extraction
        const solutionsResult = await this.generateSolutionsHelper(signal);
        if (solutionsResult.success) {
          // Clear any existing extra screenshots before transitioning to solutions view
          this.screenshotHelper.clearExtraScreenshotQueue();
          
          // Final progress update
          mainWindow.webContents.send("processing-status", {
            message: "Solution generated successfully",
            progress: 100
          });
          
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          );
          return { success: true, data: solutionsResult.data };
        } else {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          );
        }
      }

      return { success: false, error: "Failed to process screenshots" };
    } catch (error: any) {
      // If the request was cancelled, don't retry
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }
      
      // Handle OpenAI API errors specifically
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      } else if (error?.response?.status === 500) {
        return {
          success: false,
          error: "OpenAI server error. Please try again later."
        };
      }

      console.error("API Error Details:", error);
      return { 
        success: false, 
        error: error.message || "Failed to process screenshots. Please try again." 
      };
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Creating optimal solution with detailed explanations...",
          progress: 60
        });
      }

      // Create prompt for solution generation based on problem type
      const problemType = problemInfo.problem_type || "coding";

      let promptText = "";

      if (problemType === "multiple_choice") {
        promptText = `
Analyze this multiple choice question and provide the correct answer with detailed explanation:

QUESTION:
${problemInfo.problem_statement}

${problemInfo.multiple_choice_options ? `
OPTIONS:
${problemInfo.multiple_choice_options.join('\n')}
` : ""}

${problemInfo.key_concepts ? `
KEY CONCEPTS TO CONSIDER:
${problemInfo.key_concepts.join(', ')}
` : ""}

I need the response in the following format:
1. Answer: The correct option letter (A, B, C, D, etc.)
2. Explanation: Detailed reasoning for why this is the correct answer
3. Why Other Options Are Wrong: Brief explanation of why each incorrect option is wrong
4. Key Concepts: The main computer science concepts this question tests

Provide a thorough analysis that demonstrates understanding of the underlying concepts.
`;
      } else if (problemType === "conceptual") {
        promptText = `
Provide a comprehensive answer to this conceptual computer science question:

QUESTION:
${problemInfo.problem_statement}

${problemInfo.key_concepts ? `
KEY CONCEPTS TO ADDRESS:
${problemInfo.key_concepts.join(', ')}
` : ""}

I need the response in the following format:
1. Main Answer: Direct answer to the question
2. Detailed Explanation: Comprehensive explanation of the concepts involved
3. Examples: Practical examples or use cases if applicable
4. Related Concepts: Other related CS concepts that connect to this topic

Provide a thorough, educational response that demonstrates deep understanding.
`;
      } else if (problemType === "algorithm_analysis") {
        promptText = `
Analyze this algorithm or complexity question:

QUESTION:
${problemInfo.problem_statement}

${problemInfo.constraints ? `
CONSTRAINTS:
${problemInfo.constraints}
` : ""}

I need the response in the following format:
1. Analysis: Direct analysis answering the question
2. Step-by-Step Breakdown: Detailed explanation of the analysis process
3. Time Complexity: Detailed time complexity analysis with reasoning
4. Space Complexity: Detailed space complexity analysis with reasoning
5. Comparison: If applicable, comparison with alternative approaches

Provide mathematical rigor and clear explanations for all complexity analysis.
`;
      } else {
        // Default coding problem
        promptText = `
Generate a detailed solution for the following coding problem:

PROBLEM STATEMENT:
${problemInfo.problem_statement}

CONSTRAINTS:
${problemInfo.constraints || "No specific constraints provided."}

EXAMPLE INPUT:
${problemInfo.example_input || "No example input provided."}

EXAMPLE OUTPUT:
${problemInfo.example_output || "No example output provided."}

LANGUAGE: ${language}

${language === "wordpress" ? `
WORDPRESS-SPECIFIC REQUIREMENTS:
- Follow WordPress coding standards and best practices
- Use WordPress functions and hooks where appropriate (wp_query, add_action, add_filter, etc.)
- Include proper sanitization and validation for security
- Consider WordPress-specific patterns like custom post types, meta fields, and taxonomies
- Use WordPress database functions (wpdb) if database operations are needed
- Include proper error handling and user capability checks
- Follow WordPress plugin/theme development conventions
` : language === "php" ? `
PHP-SPECIFIC REQUIREMENTS:
- Follow PSR coding standards where applicable
- Use proper PHP syntax and modern PHP features
- Include proper error handling and type hints
- Consider security best practices (input validation, output escaping)
- Use appropriate PHP functions and built-in features
` : ""}

I need the response in the following format:
1. Code: A clean, optimized implementation in ${language}
2. Your Thoughts: A list of key insights and reasoning behind your approach
3. Time complexity: O(X) with a detailed explanation (at least 2 sentences)
4. Space complexity: O(X) with a detailed explanation (at least 2 sentences)

For complexity explanations, please be thorough. For example: "Time complexity: O(n) because we iterate through the array only once. This is optimal as we need to examine each element at least once to find the solution." or "Space complexity: O(n) because in the worst case, we store all elements in the hashmap. The additional space scales linearly with the input size."

Your solution should be efficient, well-commented, and handle edge cases.
`;
      }

      let responseContent;
      
      if (config.apiProvider === "openai") {
        // OpenAI processing
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }
        
        // Send to OpenAI API
        const solutionResponse = await this.openaiClient.chat.completions.create({
          model: config.solutionModel || "gpt-4o",
          messages: [
            { role: "system", content: "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." },
            { role: "user", content: promptText }
          ],
          max_tokens: 4000,
          temperature: 0.2
        });

        responseContent = solutionResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        // Gemini processing
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }
        
        try {
          // Create Gemini message structure
          const geminiMessages = [
            {
              role: "user",
              parts: [
                {
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.solutionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;
          
          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }
          
          responseContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for solution:", error);
          return {
            success: false,
            error: "Failed to generate solution with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        // Anthropic processing
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }
        
        try {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Send to Anthropic API
          const response = await this.anthropicClient.messages.create({
            model: config.solutionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          responseContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for solution:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to generate solution with Anthropic API. Please check your API key or try again later."
          };
        }
      }

      // Parse the response based on problem type
      let formattedResponse;

      // Add debugging to see what problem type was detected
      console.log("Detected problem type:", problemType);
      console.log("Problem info:", JSON.stringify(problemInfo, null, 2));

      // Check if this should be multiple choice even if not detected as such
      const hasMultipleChoiceIndicators =
        problemInfo.problem_statement?.toLowerCase().includes("select all acceptable") ||
        problemInfo.problem_statement?.toLowerCase().includes("correct statements") ||
        problemInfo.problem_statement?.toLowerCase().includes("what are the") ||
        (problemInfo.multiple_choice_options && problemInfo.multiple_choice_options.length > 0);

      if (problemType === "multiple_choice" || hasMultipleChoiceIndicators) {
        // Parse multiple choice response with more flexible patterns
        const answerRegex = /(?:Answer:|Correct Answer:|The correct answer is|Answer is)\s*([A-Z]|[0-9]+|option\s*[A-Z]|option\s*[0-9]+)/i;
        const explanationRegex = /(?:Explanation:|Reasoning:|Analysis:|Why this is correct:)([\s\S]*?)(?:Why Other Options|Key Concepts|Related Concepts|$)/i;
        const wrongOptionsRegex = /(?:Why Other Options Are Wrong:|Incorrect Options:|Why others are incorrect:)([\s\S]*?)(?:Key Concepts|Related Concepts|$)/i;
        const conceptsRegex = /(?:Key Concepts:|Concepts Tested:|Related Concepts:)([\s\S]*?)$/i;

        const answerMatch = responseContent.match(answerRegex);
        const explanationMatch = responseContent.match(explanationRegex);
        const wrongOptionsMatch = responseContent.match(wrongOptionsRegex);
        const conceptsMatch = responseContent.match(conceptsRegex);

        // If no structured answer found, try to extract from the beginning of the response
        let answerText = "Answer not clearly identified";
        if (answerMatch) {
          answerText = `Correct Answer: ${answerMatch[1]}`;
        } else {
          // Look for patterns like "The correct statements are:" or "Options A, B, and C are correct"
          const alternativeAnswerRegex = /(The correct.*?(?:are|is):?\s*)(.*?)(?:\n|$)/i;
          const altMatch = responseContent.match(alternativeAnswerRegex);
          if (altMatch) {
            answerText = `${altMatch[1]}${altMatch[2]}`;
          } else {
            // If still no answer, use the first line or paragraph as the answer
            const firstParagraph = responseContent.split('\n\n')[0] || responseContent.split('\n')[0];
            if (firstParagraph && firstParagraph.length < 200) {
              answerText = firstParagraph.trim();
            }
          }
        }

        // Extract explanation with fallback
        let explanationText = "Explanation not provided";
        if (explanationMatch) {
          explanationText = explanationMatch[1].trim();
        } else {
          // Try to find explanation in the response content
          const lines = responseContent.split('\n').filter(line => line.trim().length > 0);
          if (lines.length > 1) {
            // Use everything after the first line as explanation
            explanationText = lines.slice(1).join('\n').trim();
          }
        }

        formattedResponse = {
          code: answerText,
          thoughts: [
            explanationText,
            wrongOptionsMatch ? `Why other options are wrong: ${wrongOptionsMatch[1].trim()}` : ""
          ].filter(Boolean),
          time_complexity: conceptsMatch ? `Key Concepts: ${conceptsMatch[1].trim()}` : "Concepts not specified",
          space_complexity: "N/A for multiple choice questions"
        };
      } else if (problemType === "conceptual") {
        // Parse conceptual response
        const mainAnswerRegex = /(?:Main Answer:|Answer:)([\s\S]*?)(?:Detailed Explanation|Examples|$)/i;
        const explanationRegex = /(?:Detailed Explanation:|Explanation:)([\s\S]*?)(?:Examples|Related Concepts|$)/i;
        const examplesRegex = /(?:Examples:|Use Cases:)([\s\S]*?)(?:Related Concepts|$)/i;
        const relatedRegex = /(?:Related Concepts:|Related Topics:)([\s\S]*?)$/i;

        const mainAnswerMatch = responseContent.match(mainAnswerRegex);
        const explanationMatch = responseContent.match(explanationRegex);
        const examplesMatch = responseContent.match(examplesRegex);
        const relatedMatch = responseContent.match(relatedRegex);

        formattedResponse = {
          code: mainAnswerMatch ? mainAnswerMatch[1].trim() : responseContent,
          thoughts: [
            explanationMatch ? explanationMatch[1].trim() : "",
            examplesMatch ? `Examples: ${examplesMatch[1].trim()}` : ""
          ].filter(Boolean),
          time_complexity: relatedMatch ? `Related Concepts: ${relatedMatch[1].trim()}` : "No related concepts specified",
          space_complexity: "N/A for conceptual questions"
        };
      } else if (problemType === "algorithm_analysis") {
        // Parse algorithm analysis response
        const analysisRegex = /(?:Analysis:|Answer:)([\s\S]*?)(?:Step-by-Step|Time Complexity|$)/i;
        const stepByStepRegex = /(?:Step-by-Step Breakdown:|Breakdown:)([\s\S]*?)(?:Time Complexity|$)/i;
        const timeComplexityRegex = /(?:Time Complexity:)([\s\S]*?)(?:Space Complexity|Comparison|$)/i;
        const spaceComplexityRegex = /(?:Space Complexity:)([\s\S]*?)(?:Comparison|$)/i;
        const comparisonRegex = /(?:Comparison:|Alternative Approaches:)([\s\S]*?)$/i;

        const analysisMatch = responseContent.match(analysisRegex);
        const stepByStepMatch = responseContent.match(stepByStepRegex);
        const timeComplexityMatch = responseContent.match(timeComplexityRegex);
        const spaceComplexityMatch = responseContent.match(spaceComplexityRegex);
        const comparisonMatch = responseContent.match(comparisonRegex);

        formattedResponse = {
          code: analysisMatch ? analysisMatch[1].trim() : responseContent,
          thoughts: [
            stepByStepMatch ? stepByStepMatch[1].trim() : "",
            comparisonMatch ? `Comparison: ${comparisonMatch[1].trim()}` : ""
          ].filter(Boolean),
          time_complexity: timeComplexityMatch ? timeComplexityMatch[1].trim() : "Time complexity not specified",
          space_complexity: spaceComplexityMatch ? spaceComplexityMatch[1].trim() : "Space complexity not specified"
        };
      } else {
        // Default coding problem parsing
        const codeMatch = responseContent.match(/```(?:\w+)?\s*([\s\S]*?)```/);
        const code = codeMatch ? codeMatch[1].trim() : responseContent;

        // Extract thoughts, looking for bullet points or numbered lists
        const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)(?:Time complexity:|$)/i;
        const thoughtsMatch = responseContent.match(thoughtsRegex);
        let thoughts: string[] = [];

        if (thoughtsMatch && thoughtsMatch[1]) {
          // Extract bullet points or numbered items
          const bulletPoints = thoughtsMatch[1].match(/(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g);
          if (bulletPoints) {
            thoughts = bulletPoints.map(point =>
              point.replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim()
            ).filter(Boolean);
          } else {
            // If no bullet points found, split by newlines and filter empty lines
            thoughts = thoughtsMatch[1].split('\n')
              .map((line) => line.trim())
              .filter(Boolean);
          }
        }

        // Extract complexity information
        const timeComplexityPattern = /Time complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Space complexity|$))/i;
        const spaceComplexityPattern = /Space complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:[A-Z]|$))/i;

        let timeComplexity = "O(n) - Linear time complexity because we only iterate through the array once. Each element is processed exactly one time, and the hashmap lookups are O(1) operations.";
        let spaceComplexity = "O(n) - Linear space complexity because we store elements in the hashmap. In the worst case, we might need to store all elements before finding the solution pair.";

        const timeMatch = responseContent.match(timeComplexityPattern);
        if (timeMatch && timeMatch[1]) {
          timeComplexity = timeMatch[1].trim();
          if (!timeComplexity.match(/O\([^)]+\)/i)) {
            timeComplexity = `O(n) - ${timeComplexity}`;
          } else if (!timeComplexity.includes('-') && !timeComplexity.includes('because')) {
            const notationMatch = timeComplexity.match(/O\([^)]+\)/i);
            if (notationMatch) {
              const notation = notationMatch[0];
              const rest = timeComplexity.replace(notation, '').trim();
              timeComplexity = `${notation} - ${rest}`;
            }
          }
        }

        const spaceMatch = responseContent.match(spaceComplexityPattern);
        if (spaceMatch && spaceMatch[1]) {
          spaceComplexity = spaceMatch[1].trim();
          if (!spaceComplexity.match(/O\([^)]+\)/i)) {
            spaceComplexity = `O(n) - ${spaceComplexity}`;
          } else if (!spaceComplexity.includes('-') && !spaceComplexity.includes('because')) {
            const notationMatch = spaceComplexity.match(/O\([^)]+\)/i);
            if (notationMatch) {
              const notation = notationMatch[0];
              const rest = spaceComplexity.replace(notation, '').trim();
              spaceComplexity = `${notation} - ${rest}`;
            }
          }
        }

        formattedResponse = {
          code: code,
          thoughts: thoughts.length > 0 ? thoughts : ["Solution approach based on efficiency and readability"],
          time_complexity: timeComplexity,
          space_complexity: spaceComplexity
        };
      }

      return { success: true, data: formattedResponse };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }
      
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      }
      
      console.error("Solution generation error:", error);
      return { success: false, error: error.message || "Failed to generate solution" };
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Processing debug screenshots...",
          progress: 30
        });
      }

      // Prepare the images for the API call
      const imageDataList = screenshots.map(screenshot => screenshot.data);
      
      let debugContent;
      
      if (config.apiProvider === "openai") {
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }
        
        const messages = [
          {
            role: "system" as const, 
            content: `You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

Your response MUST follow this exact structure with these section headers (use ### for headers):
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).`
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution. Here are screenshots of my code, the errors or test cases.

${language === "wordpress" ? `
WORDPRESS-SPECIFIC DEBUGGING FOCUS:
- Check for proper WordPress function usage and hooks
- Verify security practices (sanitization, validation, nonces)
- Look for WordPress coding standards compliance
- Consider WordPress-specific error patterns and solutions
- Check for proper database queries using WordPress functions
- Verify user capability and permission checks
` : language === "php" ? `
PHP-SPECIFIC DEBUGGING FOCUS:
- Check for PHP syntax errors and warnings
- Verify proper error handling and exception management
- Look for security vulnerabilities (XSS, SQL injection, etc.)
- Check for proper variable types and function usage
- Verify PSR coding standards compliance where applicable
` : ""}

Please provide a detailed analysis with:
1. What issues you found in my code
2. Specific improvements and corrections
3. Any optimizations that would make the solution better
4. A clear explanation of the changes needed`
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        if (mainWindow) {
          mainWindow.webContents.send("processing-status", {
            message: "Analyzing code and generating debug feedback...",
            progress: 60
          });
        }

        const debugResponse = await this.openaiClient.chat.completions.create({
          model: config.debuggingModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        });
        
        debugContent = debugResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }
        
        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

${language === "wordpress" ? `
WORDPRESS-SPECIFIC DEBUGGING FOCUS:
- Check for proper WordPress function usage and hooks
- Verify security practices (sanitization, validation, nonces)
- Look for WordPress coding standards compliance
- Consider WordPress-specific error patterns and solutions
- Check for proper database queries using WordPress functions
- Verify user capability and permission checks
` : language === "php" ? `
PHP-SPECIFIC DEBUGGING FOCUS:
- Check for PHP syntax errors and warnings
- Verify proper error handling and exception management
- Look for security vulnerabilities (XSS, SQL injection, etc.)
- Check for proper variable types and function usage
- Verify PSR coding standards compliance where applicable
` : ""}

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).
`;

          const geminiMessages = [
            {
              role: "user",
              parts: [
                { text: debugPrompt },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Gemini...",
              progress: 60
            });
          }

          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.debuggingModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;
          
          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }
          
          debugContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for debugging:", error);
          return {
            success: false,
            error: "Failed to process debug request with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }
        
        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

${language === "wordpress" ? `
WORDPRESS-SPECIFIC DEBUGGING FOCUS:
- Check for proper WordPress function usage and hooks
- Verify security practices (sanitization, validation, nonces)
- Look for WordPress coding standards compliance
- Consider WordPress-specific error patterns and solutions
- Check for proper database queries using WordPress functions
- Verify user capability and permission checks
` : language === "php" ? `
PHP-SPECIFIC DEBUGGING FOCUS:
- Check for PHP syntax errors and warnings
- Verify proper error handling and exception management
- Look for security vulnerabilities (XSS, SQL injection, etc.)
- Check for proper variable types and function usage
- Verify PSR coding standards compliance where applicable
` : ""}

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification.
`;

          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: debugPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const, 
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Claude...",
              progress: 60
            });
          }

          const response = await this.anthropicClient.messages.create({
            model: config.debuggingModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });
          
          debugContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for debugging:", error);
          
          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }
          
          return {
            success: false,
            error: "Failed to process debug request with Anthropic API. Please check your API key or try again later."
          };
        }
      }
      
      
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Debug analysis complete",
          progress: 100
        });
      }

      let extractedCode = "// Debug mode - see analysis below";
      const codeMatch = debugContent.match(/```(?:[a-zA-Z]+)?([\s\S]*?)```/);
      if (codeMatch && codeMatch[1]) {
        extractedCode = codeMatch[1].trim();
      }

      let formattedDebugContent = debugContent;
      
      if (!debugContent.includes('# ') && !debugContent.includes('## ')) {
        formattedDebugContent = debugContent
          .replace(/issues identified|problems found|bugs found/i, '## Issues Identified')
          .replace(/code improvements|improvements|suggested changes/i, '## Code Improvements')
          .replace(/optimizations|performance improvements/i, '## Optimizations')
          .replace(/explanation|detailed analysis/i, '## Explanation');
      }

      const bulletPoints = formattedDebugContent.match(/(?:^|\n)[ ]*(?:[-*•]|\d+\.)[ ]+([^\n]+)/g);
      const thoughts = bulletPoints 
        ? bulletPoints.map(point => point.replace(/^[ ]*(?:[-*•]|\d+\.)[ ]+/, '').trim()).slice(0, 5)
        : ["Debug analysis based on your screenshots"];
      
      const response = {
        code: extractedCode,
        debug_analysis: formattedDebugContent,
        thoughts: thoughts,
        time_complexity: "N/A - Debug mode",
        space_complexity: "N/A - Debug mode"
      };

      return { success: true, data: response };
    } catch (error: any) {
      console.error("Debug processing error:", error);
      return { success: false, error: error.message || "Failed to process debug request" };
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    this.deps.setHasDebugged(false)

    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }
}
