export interface Solution {
  initial_thoughts: string[]
  thought_steps: string[]
  description: string
  code: string
}

export interface SolutionsResponse {
  [key: string]: Solution
}

export interface ProblemStatementData {
  problem_statement: string
  problem_type: "coding" | "multiple_choice" | "conceptual" | "algorithm_analysis"
  input_format?: {
    description: string
    parameters: any[]
  }
  output_format?: {
    description: string
    type: string
    subtype: string
  }
  complexity?: {
    time: string
    space: string
  }
  test_cases?: any[]
  validation_type?: string
  difficulty?: string
  // For multiple choice questions
  multiple_choice_options?: string[]
  correct_answer?: string
  explanation?: string
  // For conceptual questions
  key_concepts?: string[]
  // Additional fields for flexibility
  constraints?: string
  example_input?: string
  example_output?: string
}
